import type { PageListParams } from '@vben/types';

import type { OrderInfo } from './purchase';

import { requestClient } from '#/api/request';

export async function getPurchaseReturnListApi(params: PageListParams) {
  return requestClient.get<OrderInfo[]>('/scm/order/purchase/return/list', { params });
}
export async function addPurchaseReturnListApi(data: OrderInfo) {
  return requestClient.post<OrderInfo>('/scm/order/purchase/return/save', data);
}
export async function editPurchaseReturnListApi(data: OrderInfo) {
  return requestClient.post<OrderInfo>('/scm/order/purchase/return/update', data);
}
export async function infoPurchaseReturnListApi(params: OrderInfo) {
  return requestClient.get<OrderInfo>('/scm/order/purchase/return/detail', { params });
}
export async function changePurchaseReturnApi(params: OrderInfo) {
  // CANCEL：作废   DRAFT：变更
  return requestClient.post<OrderInfo>('/scm/order/purchase/return/change', params);
}
export async function delPurchaseReturnApi(id: number) {
  return requestClient.post(`/scm/order/purchase/return/delete?id=${id}`);
}
