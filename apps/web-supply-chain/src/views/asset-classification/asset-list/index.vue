<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions, formatDate } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { exportAssetApi, getAssetListApi } from '#/api/asset-classification';

import FormModal from './formModal.vue';
import HistoryModal from './historyModal.vue';

const dictStore = useDictStore();
const currentFormValues = ref({});
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Select',
      fieldName: 'latestClassification',
      label: '五级分类结果',
      componentProps: {
        options: dictStore.getDictList('ASSET_CLASSIFY_TYPE'),
      },
    },
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'projectName', title: '项目名称' },
    { field: 'latestClassification', title: '五级分类结果', formatter: ['formatStatus', 'ASSET_CLASSIFY_TYPE'] },
    {
      field: 'latestBasis',
      title: '划分依据',
      minWidth: 160,
    },
    {
      field: 'latestAdjustBasis',
      title: '调整依据',
      minWidth: 160,
    },
    {
      field: 'latestDate',
      title: '最新标记日期',
      formatter: ({ cellValue }) => formatDate(cellValue),
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        currentFormValues.value = {
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        };
        return await getAssetListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [Modal, modalApi] = useVbenModal({
  connectedComponent: FormModal,
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      gridApi.reload();
    }
  },
});
const [HisModal, hisModalApi] = useVbenModal({
  connectedComponent: HistoryModal,
});
const handleExport = async () => {
  await exportAssetApi(currentFormValues.value);
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-button class="mr-2" type="primary" @click="modalApi.setData({}).open()">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.Add') }}
        </a-button>
        <a-button class="mr-2" type="primary" @click="handleExport"> 导出 </a-button>
      </template>
      <template #action="{ row }">
        <a-space>
          <TypographyLink @click="modalApi.setData(row).open()"> 划分/调整分类 </TypographyLink>
          <TypographyLink @click="hisModalApi.setData(row).open()"> 分类历史记录 </TypographyLink>
        </a-space>
      </template>
    </Grid>
    <Modal />
    <HisModal />
  </Page>
</template>

<style></style>
