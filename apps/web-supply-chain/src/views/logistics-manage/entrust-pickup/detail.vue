<script setup lang="ts">
import type { DeliveryBaseInfo, ProjectBaseInfo } from '#/api';

import { reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { cloneDeep, formatDate } from '@vben/utils';

import { Descriptions, DescriptionsItem } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { detailDeliveryApi, getCompanyApi, projectManageListApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['register', 'ok']);

const { useViewButton, initWorkflow, isWorkflow, isRunningTask } = useWorkflowBase();
const pageType = ref('detail');
const ViewButton = useViewButton();
const { getDictList } = useDictStore();

const belongProjectOptions = ref<ProjectBaseInfo[]>([]);

const loading = reactive({
  submit: false,
});

// 默认数据
const defaultForm: Partial<DeliveryBaseInfo> = {
  id: undefined,
  docCode: undefined,
  projectId: undefined,
  projectName: undefined,
  projectCode: undefined,
  entrustedCompanyCode: undefined,
  entrustedCompanyName: undefined,
  entrustedDate: undefined,
  status: undefined,
  approvalStatus: undefined,
  originalFileId: undefined,
  signedFileId: undefined,
  userId: undefined,
  userName: undefined,
  organId: undefined,
  organName: undefined,
  description: undefined,
  version: undefined,
  attachmentList: [],
};

const detailForm = reactive<Partial<DeliveryBaseInfo>>(cloneDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };

const companyOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};

const title = '详情';

const init = async (data: any) => {
  await getCompanyList();
  pageType.value = data.pageType;
  await initWorkflow({ formKey: 'scm_delivery_pickup_doc', businessKey: data.id });
  const res = await projectManageListApi({ projectName: '' });
  belongProjectOptions.value = res
    .filter((item) => item.id !== undefined) // 过滤掉 id 为 undefined 的项
    .map((item) => ({
      projectName: item.projectName,
      id: item.id as number | string, // 类型断言
    }));
  if (data.id) {
    const res = await detailDeliveryApi(data.id);
    Object.assign(detailForm, cloneDeep(res));
  } else {
    Object.assign(detailForm, cloneDeep(defaultForm));
  }
};

// 处理企业code转换为名称显示
const getCompanyLabel = (companyList: any[], code: string | undefined) => {
  if (!code) return '';
  const item = companyList.find((item) => item.companyCode === code);
  return item ? item.companyName : code;
};

const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup">
    <template #insertToolbar>
      <ViewButton v-if="isWorkflow && pageType === 'detail'" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <DescriptionsItem label="委托提货函编号">
          {{ detailForm.docCode }}
        </DescriptionsItem>

        <DescriptionsItem label="受托企业">
          {{ getCompanyLabel(companyOptions, detailForm.entrustedCompanyCode) }}
        </DescriptionsItem>

        <DescriptionsItem label="项目名称">
          {{ detailForm.projectName }}
        </DescriptionsItem>

        <!--        <DescriptionsItem label="项目编号">-->
        <!--          {{ detailForm.projectCode }}-->
        <!--        </DescriptionsItem>-->

        <DescriptionsItem label="委托日期">
          {{ formatDate(detailForm.entrustedDate, 'YYYY-MM-DD') }}
        </DescriptionsItem>

        <DescriptionsItem label="经办人">
          {{ detailForm.userName }}
        </DescriptionsItem>

        <DescriptionsItem label="经办部门">
          {{ detailForm.organName }}
        </DescriptionsItem>

        <!-- 备注 -->
        <DescriptionsItem label="备注">
          {{ detailForm.description }}
        </DescriptionsItem>
      </Descriptions>
      <!--   附件信息   -->
      <BaseAttachmentList
        border="inner"
        v-model="detailForm.attachmentList"
        :business-id="detailForm.id"
        business-type="SCM_DELIVERY_PICKUP_DOC"
      />
    </div>
  </BasicPopup>
</template>

<style scoped>
:deep(.ant-picker) {
  width: 100%;
}
</style>
