<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { OrderInfo } from '#/api';

import { ref, watch } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP, DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';
import { formatDate, formatMoney } from '@vben/utils';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { BusinessStructureEnum, infoPurchaseListApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['register', 'ok']);
const { useOperationButton, useViewButton, initWorkflow, isWorkflow, isWorkflowLoading, isProcessInstance } =
  useWorkflowBase();
const orderDetail = ref<Partial<OrderInfo>>({});
const dictStore = useDictStore();
const init = async (data: OrderInfo & { pageType: string }) => {
  pageType.value = data.pageType;
  await initWorkflow({ formKey: 'scm_purchase_order', businessKey: data.id });
  if (data?.id) {
    orderDetail.value = await infoPurchaseListApi({ id: data.id });
  }
};
const pageType = ref('detail');
const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const [registerPopup, { closePopup }] = usePopupInner(init);

const columns = [
  { field: 'total' },
  { field: 'itemNumber', title: '行号', minWidth: '150px' },
  // { field: 'categoryName', title: '商品分类' },
  { field: 'productName', title: '商品名称', minWidth: '150px' },
  { field: 'productCode', title: '商品编码', minWidth: '150px' },
  { field: 'productAlias', title: '商品别名', minWidth: '150px' },
  { field: 'specifications', title: '规格型号', minWidth: '150px' },
  { field: 'measureUnit', title: '单位', minWidth: '150px' },
  // { field: 'brandName', title: '牌号/材质', minWidth: '150px' },
  // { field: 'originName', title: '产地/厂商', minWidth: '150px' },
  { field: 'quantity', title: '重量', minWidth: '150px' },
  {
    field: 'priceWithTax',
    title: '含税单价',
    minWidth: '150px',
    formatter: 'formatMoney',
  },
  // {
  //   field: 'priceWithoutTax',
  //   title: '不含税单价',
  //   minWidth: '150px',
  //   formatter: 'formatMoney',
  // },
  { field: 'taxRate', title: '税率(%)', minWidth: '150px' },
  {
    field: 'amountWithTax',
    title: '含税金额',
    minWidth: '150px',
    formatter: 'formatMoney',
  },
  { field: 'taxAmount', title: '税额', minWidth: '150px', formatter: 'formatMoney' },
  // {
  //   field: 'amountWithoutTax',
  //   title: '不含税金额',
  //   minWidth: '150px',
  //   formatter: 'formatMoney',
  // },
  {
    field: 'taxAmount',
    title: '税额',
    formatter: 'formatMoney',
    width: '150px',
  },
  { field: 'sourceItemNumber', title: '销售行号', minWidth: '150px', slots: { default: 'sourceItemNumber' } },
  { field: 'sourcePrice', title: '销售含税单价', minWidth: '150px', formatter: 'formatMoney' },
  { field: 'remarks', title: '备注', minWidth: '200px' },
];
const bankGridOptions = {
  showFooter: true,
  footerMethod() {
    const footerRow = {
      total: '合计',
      amountWithTax: formatMoney(orderDetail.value.totalAmountWithTax),
      taxAmount: formatMoney(orderDetail.value.totalTaxAmount),
      amountWithoutTax: formatMoney(orderDetail.value.totalAmountWithoutTax),
    };
    return [footerRow];
  },
  columns,
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [ProducGrid, bankGridApi] = useVbenVxeGrid({
  gridOptions: bankGridOptions,
});
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
watch(
  () => orderDetail.value,
  (val = {}) => {
    bankGridApi.grid.reloadData(val.purchaseOrderItem ?? []);
    bankGridOptions.columns =
      val.businessStructure === BusinessStructureEnum.GENERAL
        ? columns.filter((item) => item.field !== 'sourceItemNumber' && item.field !== 'sourcePrice')
        : columns;
    bankGridApi.setGridOptions(bankGridOptions);
  },
  { deep: true },
);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="订单信息" @register="registerPopup">
    <template #insertToolbar>
      <ViewButton v-if="isWorkflow && pageType === 'detail'" />
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton v-if="isProcessInstance" @success="workflowSuccess" />
      </div>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="订单编号">
          {{ orderDetail.purchaseOrderCode }}
        </a-descriptions-item>
        <a-descriptions-item label="订单名称">
          {{ orderDetail.purchaseOrderName }}
        </a-descriptions-item>
        <a-descriptions-item label="项目编号">
          {{ orderDetail.projectCode }}
        </a-descriptions-item>
        <a-descriptions-item label="项目名称">
          {{ orderDetail.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="业务结构">
          {{ dictStore.formatter(orderDetail.businessStructure, 'BUS_STRUCTURE') }}
        </a-descriptions-item>
        <a-descriptions-item label="项目模式">
          {{ dictStore.formatter(orderDetail.projectModel, 'PROJECT_MODE') }}
        </a-descriptions-item>
        <a-descriptions-item
          label="关联销售订单"
          v-if="orderDetail.businessStructure === BusinessStructureEnum.PURCHASE"
        >
          {{ orderDetail.salesOrderName }}
        </a-descriptions-item>
        <a-descriptions-item
          label="销售订单编号"
          v-if="orderDetail.businessStructure === BusinessStructureEnum.PURCHASE"
        >
          {{ orderDetail.salesOrderCode }}
        </a-descriptions-item>
        <a-descriptions-item label="贸易执行企业">
          {{ orderDetail.executorCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="上游企业">
          {{ orderDetail.supplierCompanyName }}
        </a-descriptions-item>
        <!--<a-descriptions-item label="业务负责人">-->
        <!--  {{ orderDetail.businessManagerName }}-->
        <!--</a-descriptions-item>-->
        <!--<a-descriptions-item label="运营负责人">-->
        <!--  {{ orderDetail.operationManagerName }}-->
        <!--</a-descriptions-item>-->
        <a-descriptions-item label="业务日期">
          {{ formatDate(orderDetail.businessDate || '') }}
        </a-descriptions-item>
        <a-descriptions-item label="预计结束日期">
          {{ formatDate(orderDetail.estimatedEndDate || '') }}
        </a-descriptions-item>
        <a-descriptions-item label="账期（天)" v-if="orderDetail.businessStructure === BusinessStructureEnum.SALE">
          {{ orderDetail.paymentTermDays }}
        </a-descriptions-item>
        <a-descriptions-item label="截止交货日期">
          {{ formatDate(orderDetail.deliveryDeadline || '') }}
        </a-descriptions-item>
        <a-descriptions-item
          label="预付款比例(%)"
          v-if="orderDetail.businessStructure === BusinessStructureEnum.PURCHASE"
        >
          {{ orderDetail.prepaymentRatio }}
        </a-descriptions-item>
        <a-descriptions-item label="预付款金额" v-if="orderDetail.businessStructure === BusinessStructureEnum.PURCHASE">
          {{ formatMoney(orderDetail.prepaymentAmount) }}
        </a-descriptions-item>
        <a-descriptions-item label="保证金金额" v-if="orderDetail.businessStructure === BusinessStructureEnum.SALE">
          {{ formatMoney(orderDetail.depositAmount) }}
        </a-descriptions-item>
        <!--<a-descriptions-item label="垫资比例(%)" v-if="orderDetail.businessStructure === BusinessStructureEnum.SALE">-->
        <!--  {{ formatMoney(orderDetail.advanceRatio) }}-->
        <!--</a-descriptions-item>-->
        <a-descriptions-item label="任务类型">
          {{ dictStore.formatter(orderDetail.taskType, 'TASK_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="2">
          {{ orderDetail.remarks }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="商品信息" />
      <ProducGrid>
        <template #sourceItemNumber="{ row }"> {{ row.sourceOrderCode }}-{{ row.sourceItemNumber }} </template>
      </ProducGrid>
      <BaseAttachmentList border="inner" :business-id="orderDetail.id" business-type="SCM_PURCHASE_ORDER" />
    </div>
  </BasicPopup>
</template>

<style></style>
