<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Button, Space, TypographyLink, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { type OperationSuperviseBaseInfo, operationSupervisePageApi, operationSuperviseDeleteApi } from '#/api';

import Create from './create.vue';
import Detail from './detail.vue';
import { ref } from 'vue';
import { useModalUrl } from '@vben/base-ui';

const { getDictList } = useDictStore();

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'reportName',
      label: '报告名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '业务状态',
      componentProps: {
        options: getDictList('BUS_STATUS'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      componentProps: {
        options: getDictList('REVIEW_STATUS'),
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'reportCode',
      label: '报告编号',
    },
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { field: 'reportCode', title: '报告编号' },
    { field: 'reportName', title: '报告名称' },
    { field: 'reportDate', title: '报告日期', formatter: 'formatDate' },
    {
      field: 'status',
      title: '业务状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BUS_STATUS',
        },
      },
    },
    {
      field: 'approvalStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    { field: 'remarks', title: '备注' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await operationSupervisePageApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: sortKey.value,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const sortKey = ref<string>('create_time');
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 创建
const add = () => {
  openFormPopup(true, { pageType: 'edit' });
};

// 编辑
const edit = (row: OperationSuperviseBaseInfo) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};

// 查看
const detail = (row: OperationSuperviseBaseInfo) => {
  openDetailPopup(true, { ...row, pageType: 'detail' });
};

// 删除
const del = async () => {
  const rows = gridApi.grid.getCheckboxRecords();
  if (rows.length === 0) {
    return message.warning('请至少选择一条数据');
  }

  // 确认删除操作
  AntdModal.confirm({
    title: '确认删除',
    content: `确定要删除选中的 ${rows.length} 条记录吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 批量删除所有选中的记录
        const ids = rows.map((row) => row.id);
        await Promise.all(ids.map((id) => operationSuperviseDeleteApi(id)));
        await gridApi.reload();
        message.success(`成功删除 ${rows.length} 条记录`);
      } catch (error) {
        message.error('删除失败');
      }
    },
  });
};

const access = (row: { id: number }) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};
const viewDetail = (row: { id: number }) => {
  openDetailPopup(true, { ...row, pageType: 'detail' });
};
const audit = (row: { id: number }) => {
  openFormPopup(true, { ...row, pageType: 'audit' });
};

useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data: AccessInfo = {
        id: params.id,
      };
      access(data);
    },

    // 详情弹层
    detail: (params) => {
      const data: AccessInfo = {
        id: params.id,
      };
      viewDetail(data);
    },

    // 审核弹层
    audit: (params) => {
      const data: AccessInfo = {
        id: params.id,
        pageType: 'audit',
      };
      audit(data);
    },
  },
});

const createSuccess = () => {
  gridApi.formApi.submitForm();
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
        <Button type="primary" danger @click="del">删除</Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink v-if="row.status === 'DRAFTING' || row.approvalStatus === 'REJECTED'" @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Create @register="registerForm" @ok="createSuccess" />
    <Detail @register="registerDetail" />
  </Page>
</template>

<style></style>
