import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

// 产品信息
export interface AssetClassificationInfo {
  id?: string;
  projectApplyId?: string;
  projectType?: string;
  // 资产编号
  assetCode?: string;
  // 所属项目id
  projectId?: string;
  // 所属项目编号
  projectCode?: string;
  // 所属项目名称
  projectName?: string;
  // 企业编码
  companyCode?: string;
  // 企业名称
  companyName?: string;
  // 分类结果
  latestClassification?: string;
  // 标记日期
  latestDate?: string;
  // 划分依据
  latestBasis?: string;
  // 调整依据
  latestAdjustBasis?: string;
  executorCompanyName?: string;
  executorCompanyCode?: string;
  remarks?: string;
}

export async function getAssetListApi(params: PageListParams) {
  return requestClient.get('/factoring/risk/asset/classification/list', { params });
}
export async function addAssetApi(params: AssetClassificationInfo) {
  return requestClient.post('/factoring/risk/asset/classification/add', params);
}
export async function updateAssetApi(params: AssetClassificationInfo) {
  return requestClient.post('/factoring/risk/asset/classification/update', params);
}
export async function getAssetHistoryApi(params: { id: string }) {
  return requestClient.get('/factoring/risk/asset/classification/history', { params });
}
export async function exportAssetApi(data: any) {
  return requestClient.downloadAndSave(`/factoring/risk/asset/classification/export`, {
    config: { params: data },
  });
}
