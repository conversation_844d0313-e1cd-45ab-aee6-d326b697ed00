import { requestClient } from '#/api/request';

export interface OverviewInfo {
  [property: string]: any;
}

// 获取风险预警
export async function getStatisticsRiskApi(params) {
  return requestClient.get<OverviewInfo[]>('/factoring/statistics/risk/warn', { params });
}

// 实时核心指标
export async function getStatisticsProjectRealApi(params) {
  return requestClient.get<OverviewInfo[]>('/factoring/statistics/project/real', { params });
}

// 进行中项目按类型分布
export async function getStatisticsProjectIndustryApi(params) {
  return requestClient.get<OverviewInfo[]>('/factoring/statistics/project/industry', { params });
}

// 进行中项目按类型分布
export async function getStatisticsProjectFactoryApi(params) {
  return requestClient.get<OverviewInfo[]>('/factoring/statistics/project/factory/type', { params });
}

// 累计服务客户数量
export async function getStatisticsCustomerCumulativeApi(params) {
  return requestClient.get<OverviewInfo[]>('/factoring/statistics/customer/cumulative', { params });
}

// 合作中客户数量
export async function getStatisticsCustomerCooperationApi(params) {
  return requestClient.get<OverviewInfo[]>('/factoring/statistics/customer/cooperation', { params });
}

// 本年新增客户数量
export async function getStatisticsCustomerAddApi(params) {
  return requestClient.get<OverviewInfo[]>('/factoring/statistics/customer/add', { params });
}

// 资产风险分类
export async function getStatisticsAssetClassificationApi(params) {
  return requestClient.get<OverviewInfo[]>('/factoring/statistics/asset/classification', { params });
}
