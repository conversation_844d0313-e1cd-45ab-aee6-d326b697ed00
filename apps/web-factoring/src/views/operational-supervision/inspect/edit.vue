<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { CheckItem } from './components/ScoreTable.vue';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { InspectVO } from '#/api';

import { computed, reactive, ref } from 'vue';

import { ImportData } from '@vben/base-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';
import { cloneDeep, formatDate, formatMoney } from '@vben/utils';

import { Button, Col, DatePicker, Empty, Form, FormItem, Input, message, Row, Select, Textarea } from 'ant-design-vue';
import dayjs from 'dayjs';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addInspectApi,
  downloadTemplateApi,
  editInspectApi,
  getAssetListApi,
  getCreditApplyInfoApi,
  getInspectDetailApi,
  getOverviewInfoApi,
  getPaymentRecordPageListApi,
  getPricingInfoApi,
  getProjectContractDetail,
  getProjectLimitApi,
  getQuarterlyApi,
  getUserListApi,
  importApi,
} from '#/api';

import FinancialReportTable from './components/FinancialReportTable.vue';
import { generateTemplate } from './components/inspect';
import ScoreTable from './components/ScoreTable.vue';

const emit = defineEmits(['register', 'ok']);

const initReport = (): InspectVO => ({
  analysisQuarter: '',
  analysisYear: '',
  assetClassification: '',
  reportCode: '',
  attachmentList: undefined,
  businessAmount: undefined,
  continuationDesc: undefined,
  contractEndDate: undefined,
  contractStartDate: undefined,
  creditEnhancementDesc: undefined,
  creditorFinancialAnalysisDesc: undefined,
  creditorInvestmentMap: undefined,
  creditorList: [],
  debtorFinancialAnalysisDesc: undefined,
  debtorInvestmentMap: undefined,
  debtorList: [],
  factoringType: undefined,
  fundFlowDesc: undefined,
  guarantorFinancialAnalysisDesc: undefined,
  guarantorInvestmentMap: undefined,
  guarantorList: [],
  inspectionBys: [],
  inspectionConclusionDesc: undefined,
  inspectionDate: undefined,
  inspectionMethod: '',
  investmentAmount: undefined,
  investmentDate: undefined,
  isSubmit: undefined,
  operationBys: [],
  otherDesc: undefined,
  projectId: undefined,
  projectType: undefined,
  receivableDesc: undefined,
  reportDate: undefined,
  reportName: '',
  reviewerBys: [],
  riskManagementDesc: undefined,
  underlyingProjectDesc: undefined,
  xirrRate: undefined,
  id: undefined,
});
const reportForm = reactive<InspectVO>(initReport());

const quarterOptions = [
  { value: 'Q1', label: '第一季度' },
  { value: 'Q2', label: '第二季度' },
  { value: 'Q3', label: '第三季度' },
  { value: 'Q4', label: '第四季度' },
];

const creditorActiveKey = ref('');
const debtorActiveKey = ref('');
const guarantorActiveKey = ref('');

const creditorScoreTableDataMap = reactive<Record<string, CheckItem[]>>({});
const creditorFinanceTableDataMap = reactive<Record<string, any[]>>({});
const debtorScoreTableDataMap = reactive<Record<string, CheckItem[]>>({});
const guarantorScoreTableDataMap = reactive<Record<string, CheckItem[]>>({});
const guarantorFinanceTableDataMap = reactive<Record<string, any[]>>({});

const creditorFinancialAnalysisDesc = reactive<Record<string, string>>({});
const guarantorFinancialAnalysisDesc = reactive<Record<string, string>>({});

const creditorScoreTableRefs = ref<Record<string, any>>({});
const debtorScoreTableRefs = ref<Record<string, any>>({});
const guarantorScoreTableRefs = ref<Record<string, any>>({});

const colSpan = COL_SPAN_PROP;
const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 4 }, wrapperCol: { span: 20 } };

const rules = computed<Record<string, Rule[]>>(() => {
  const base: Record<string, Rule[]> = {
    inspectionBys: [{ required: true, message: '请选择检查人员', trigger: 'change' }],
    inspectionMethod: [{ required: true, message: '请选择检查方式', trigger: 'change' }],
    assetClassification: [{ required: true, message: '请选择资产分类', trigger: 'change' }],
    operationBys: [{ required: true, message: '请输入运营人员', trigger: 'change' }],
    reviewerBys: [{ required: true, message: '请选择复核人员', trigger: 'change' }],
  };
  return base;
});
const title = computed(() => {
  return reportForm.id ? '编辑' : '新增';
});

const formRef = ref();

const clearReactiveObject = (obj: Record<string, any>) => {
  Object.keys(obj).forEach((key) => delete obj[key]);
};
const initData = () => {
  Object.assign(reportForm, initReport());
  clearReactiveObject(creditorFinanceTableDataMap);
  clearReactiveObject(creditorScoreTableDataMap);
  clearReactiveObject(creditorFinancialAnalysisDesc);
  clearReactiveObject(guarantorFinanceTableDataMap);
  clearReactiveObject(guarantorScoreTableDataMap);
  clearReactiveObject(guarantorFinancialAnalysisDesc);
  clearReactiveObject(debtorScoreTableDataMap);
};

const init = async (data: any) => {
  if (data.id) {
    const res = await getInspectDetailApi(data.id);
    Object.assign(reportForm, res);
    reportForm.creditorList = res.creditorList || [];
    reportForm.debtorList = res.debtorList || [];
    reportForm.guarantorList = res.guarantorList || [];
    reportForm.projectCreditApplyName = res.creditApplyName;
    reportForm.inspectionDate = reportForm.inspectionDate && dayjs(reportForm.inspectionDate).valueOf().toString();
    reportForm.reportDate = reportForm.reportDate && dayjs(reportForm.reportDate).valueOf().toString();
    // reportForm.investmentDate = reportForm.investmentDate && dayjs(reportForm.investmentDate).valueOf().toString();
    gridPerformanceApi.grid.reloadData(res.paymentList || []);
    if (reportForm.creditorList?.length > 0) {
      creditorActiveKey.value = reportForm.creditorList[0].companyCode;
    }
    if (reportForm.debtorList?.length > 0) {
      debtorActiveKey.value = reportForm.debtorList[0].companyCode;
    }
    if (reportForm.guarantorList?.length > 0) {
      guarantorActiveKey.value = reportForm.guarantorList[0].companyCode;
    }

    Object.assign(creditorFinanceTableDataMap, res.creditorFinanceMap);
    Object.assign(creditorScoreTableDataMap, res.creditorInvestmentMap);
    Object.assign(creditorFinancialAnalysisDesc, res.creditorFinancialAnalysisDesc);
    Object.assign(guarantorFinanceTableDataMap, res.guarantorFinanceMap);
    Object.assign(guarantorScoreTableDataMap, res.guarantorInvestmentMap);
    Object.assign(guarantorFinancialAnalysisDesc, res.guarantorFinancialAnalysisDesc);
    Object.assign(debtorScoreTableDataMap, res.debtorInvestmentMap);
    reportForm.inspectionBys =
      reportForm.inspectionBys && reportForm.inspectionBys.map((item) => ({ key: item.userId, label: item.userName }));
    reportForm.operationBys =
      reportForm.operationBys && reportForm.operationBys.map((item) => ({ key: item.userId, label: item.userName }));
    reportForm.reviewerBys = reportForm.reviewerBys && reportForm.reviewerBys[0];
    reportForm.reviewerBys = { key: reportForm.reviewerBys.userId, label: reportForm.reviewerBys.userName };
  } else {
    initData();
    Object.assign(reportForm, data);
    await getProjectInfo(data);
  }
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner((data) => init(data));
const gridPerformanceTable: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  data: reportForm.paymentList,
  columns: [
    { field: 'repaymentDate', title: '还本付息日', formatter: 'formatDate' },
    { field: 'repaymentAmount', title: '应还款金额（元）', formatter: 'formatMoney' },
    { field: 'actualRepaymentDate', title: '实际回款日', formatter: 'formatDate' },
    { field: 'actualRepaymentAmount', title: '实际回款金额（元）', formatter: 'formatMoney' },
    {
      field: 'overdueStatus',
      title: '是否逾期',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_INSPECTION_OVERDUE_STATUS',
        },
      },
      width: 150,
    },
  ],
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const dictStore = useDictStore();
const handleSave = async (isSubmit = false) => {
  try {
    await formRef.value.validate();
    changeOkLoading(true);
    for (const companyCode in creditorScoreTableRefs.value) {
      const tableRef = creditorScoreTableRefs.value[companyCode];
      if (tableRef && typeof tableRef.getTableData === 'function') {
        creditorScoreTableDataMap[companyCode] = tableRef.getTableData();
      }
    }
    for (const companyCode in debtorScoreTableRefs.value) {
      const tableRef = debtorScoreTableRefs.value[companyCode];
      if (tableRef && typeof tableRef.getTableData === 'function') {
        debtorScoreTableDataMap[companyCode] = tableRef.getTableData();
      }
    }
    for (const companyCode in guarantorScoreTableRefs.value) {
      const tableRef = guarantorScoreTableRefs.value[companyCode];
      if (tableRef && typeof tableRef.getTableData === 'function') {
        guarantorScoreTableDataMap[companyCode] = tableRef.getTableData();
      }
    }

    reportForm.paymentList = gridPerformanceApi.grid.getTableData().fullData;
    reportForm.creditorInvestmentMap = creditorScoreTableDataMap;
    reportForm.creditorFinancialAnalysisDesc = creditorFinancialAnalysisDesc;
    reportForm.creditorFinanceMap = creditorFinanceTableDataMap;

    reportForm.debtorInvestmentMap = debtorScoreTableDataMap;

    reportForm.guarantorInvestmentMap = guarantorScoreTableDataMap;
    reportForm.guarantorFinanceMap = guarantorFinanceTableDataMap;
    reportForm.guarantorFinancialAnalysisDesc = guarantorFinancialAnalysisDesc;

    reportForm.inspectionBys =
      reportForm.inspectionBys &&
      reportForm.inspectionBys.map((item) => ({ ...item, userId: item.key, userName: item.label }));
    reportForm.reviewerBys = [
      { ...reportForm.reviewerBys, userId: reportForm.reviewerBys?.key, userName: reportForm.reviewerBys?.label },
    ];
    reportForm.operationBys =
      reportForm.operationBys &&
      reportForm.operationBys.map((item) => ({ ...item, userId: item.key, userName: item.label }));
    const params = cloneDeep(reportForm);
    if (isSubmit) {
      params.isSubmit = true;
    }
    params.id ? await editInspectApi(params) : await addInspectApi(params);

    message.success('保存成功');
    closePopup();
    initData();
    emit('ok');
  } catch (error) {
    console.error(`${isSubmit ? '提交' : '保存'}失败:`, error);
  } finally {
    changeOkLoading(false);
  }
};

const save = async () => {
  handleSave(false);
};

const submit = async () => {
  handleSave(true);
};

const close = () => {
  initData();
};
// 工具方法
const pickCompany = (list: any[], type: string) => list?.filter((item) => item.projectCompanyType === type) ?? [];
// 工具方法
const names = (list: any[]) => list.map((i) => i.companyName || '').join(',');

const getProjectInfo = async ({ projectId, analysisYear, analysisQuarter, projectApplyId }: any) => {
  const performanceRes = await getQuarterlyApi({ projectId, analysisYear, analysisQuarter });
  gridPerformanceApi.grid.reloadData(performanceRes);

  const overview = await getOverviewInfoApi(projectId);
  if (!overview) return;
  const { creditEnhancementDesc, factoringType, companyList = [] } = overview;
  const assetList = await getAssetListApi({ projectId, projectApplyId });
  const res = await getProjectLimitApi({ projectId });
  const contract = await getProjectContractDetail(projectId);
  const assetClassification = assetList?.records?.[0]?.latestClassification;
  Object.assign(reportForm, {
    contractStartDate: contract?.contractStartDate,
    contractEndDate: contract?.contractEndDate,
    creditEnhancementDesc,
    // investmentDate,
    factoringType,
    assetClassification,
    businessAmount: res.stockBalance,
  });

  if (reportForm.projectApplyId) {
    Object.assign(reportForm, {
      investmentAmount: undefined,
      xirrRate: undefined,
      creditorListString: '',
      debtorListString: '',
      guarantorListString: '',
      creditorList: [],
      debtorList: [],
      guarantorList: [],
    });
    const performanceRes = await getQuarterlyApi({
      projectId,
      projectApplyId: reportForm.projectApplyId,
      analysisYear,
      analysisQuarter,
    });
    gridPerformanceApi.grid.reloadData(performanceRes);

    const res = await getCreditApplyInfoApi(reportForm.projectApplyId);
    if (!res) return;
    const { companyList = [], calculation, projectCreditApplyName, factoringType } = res;
    const payeeDate = await getPaymentRecordPageListApi({ projectApplyId: reportForm.projectApplyId });
    if (payeeDate?.records?.length > 0) {
      reportForm.investmentDate =
        payeeDate?.records
          ?.map((r) => r?.confirmInvestDate)
          .filter(Boolean)
          .map((d) => dayjs(d).format('YYYY-MM-DD'))
          .join(',') || undefined;
      reportForm.investmentAmount = Number(
        payeeDate.records.reduce((sum, r) => sum + (r?.confirmInvestAmount || 0), 0),
      );
    }
    const creditors = pickCompany(companyList, 'creditor');
    const debtors = pickCompany(companyList, 'debtor');
    const guarantors = pickCompany(companyList, 'guarantee');

    Object.assign(reportForm, {
      creditorList: creditors,
      debtorList: debtors,
      projectCreditApplyName,
      guarantorList: guarantors,
      creditorListString: names(creditors),
      debtorListString: names(debtors),
      guarantorListString: names(guarantors),
      xirrRate: calculation?.pricingXirrRate,
      factoringType,
    });

    if (reportForm.creditorList?.length > 0) {
      creditorActiveKey.value = reportForm.creditorList[0].companyCode;
    }
    if (reportForm.debtorList?.length > 0) {
      debtorActiveKey.value = reportForm.debtorList[0].companyCode;
    }
    if (reportForm.guarantorList?.length > 0) {
      guarantorActiveKey.value = reportForm.guarantorList[0].companyCode;
    }
  } else {
    const pricing = await getPricingInfoApi(overview?.pricing);
    reportForm.xirrRate = pricing?.pricingXirrRate;
    const payeeDate = await getPaymentRecordPageListApi({ projectId: reportForm.projectId });
    if (payeeDate?.records?.length > 0) {
      reportForm.investmentDate =
        payeeDate?.records
          ?.map((r) => r?.confirmInvestDate)
          .filter(Boolean)
          .map((d) => dayjs(d).format('YYYY-MM-DD'))
          .join(',') || undefined;
      reportForm.investmentAmount = Number(
        payeeDate.records.reduce((sum, r) => sum + (r?.confirmInvestAmount || 0), 0),
      );
    }
    const creditors = pickCompany(companyList, 'creditor');
    const debtors = pickCompany(companyList, 'debtor');
    const guarantors = pickCompany(companyList, 'guarantee');

    creditors.forEach((item) => {
      if (!creditorScoreTableDataMap[item.companyCode]) {
        creditorScoreTableDataMap[item.companyCode] = generateTemplate(); // 或者 []
      }
    });
    Object.assign(reportForm, {
      creditorList: creditors,
      debtorList: debtors,
      guarantorList: guarantors,
      creditorListString: names(creditors),
      debtorListString: names(debtors),
      guarantorListString: names(guarantors),
    });

    if (reportForm.creditorList?.length > 0) {
      creditorActiveKey.value = reportForm.creditorList[0].companyCode;
    }
    if (reportForm.debtorList?.length > 0) {
      debtorActiveKey.value = reportForm.debtorList[0].companyCode;
    }
    if (reportForm.guarantorList?.length > 0) {
      guarantorActiveKey.value = reportForm.guarantorList[0].companyCode;
    }
  }
};

const userOptions = ref<{ userId: number; userName: string }[]>([]);

const loadUserOptions = async () => {
  userOptions.value = await getUserListApi();
};
const filterOption = (input: string, option: any) => {
  return option.realName.toLowerCase().includes(input.toLowerCase());
};
loadUserOptions();

const transformFinancialReport = (data: Record<string, string>[][]): Record<string, string>[] => {
  if (!Array.isArray(data) || data.length === 0) return [];

  const resultMap: Record<string, Record<string, string>> = {};
  data.forEach((monthGroup) => {
    const monthKey = Object.keys(monthGroup[0] || {}).find((k) => k !== '经营成果（元）') as string;

    monthGroup.forEach((row) => {
      const key = row['经营成果（元）'];
      if (!resultMap[key]) {
        resultMap[key] = { '经营成果（元）': key };
      }
      resultMap[key][monthKey] = row[monthKey];
    });
  });

  return Object.values(resultMap);
};

const handleImportSuccess = (code: string, data: Record<string, string>[][], type: string) => {
  const transformed = transformFinancialReport(data);
  if (type === 'creditor') {
    creditorFinanceTableDataMap[code] = transformed;
  } else {
    guarantorFinanceTableDataMap[code] = transformed;
  }
};

const [PerformanceTable, gridPerformanceApi] = useVbenVxeGrid({ gridOptions: gridPerformanceTable });
</script>

<template>
  <BasicPopup
    v-bind="$attrs"
    show-ok-btn
    :title="title"
    @register="registerPopup"
    @ok="save"
    @close="close"
    destroyon-close
  >
    <template #centerToolbar>
      <Button class="ml-2" type="primary" @click="submit">提交</Button>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <Form ref="formRef" :model="reportForm" :rules="rules" v-bind="formProp" class="px-8">
        <!-- 基本信息 -->
        <Row class="mt-5">
          <Col v-bind="colSpan">
            <FormItem label="项目名称" name="projectId">
              {{ reportForm.projectName }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="用信名称" v-show="reportForm.projectType === 'comprehensive'" name="projectApplyId">
              {{ reportForm.projectCreditApplyName }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="运营分析报告名称" name="reportName">
              {{ reportForm.reportName }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="运营分析报告编号">
              {{ reportForm.reportCode }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="分析年份" name="analysisYear">
              {{ reportForm.analysisYear }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="分析季度" name="analysisQuarter">
              {{
                quarterOptions.find((q) => q.value === reportForm.analysisQuarter)?.label ||
                reportForm.analysisQuarter ||
                '-'
              }}
            </FormItem>
          </Col>
        </Row>
        <BasicCaption content="一、基本情况" />
        <Row class="mt-5">
          <Col v-bind="colSpan">
            <FormItem label="检查人员" name="inspectionBys">
              <Select
                v-model:value="reportForm.inspectionBys"
                mode="multiple"
                allow-clear
                label-in-value
                show-search
                :options="userOptions"
                :field-names="{ label: 'realName', value: 'id' }"
                :filter-option="filterOption"
              />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="检查方式" name="inspectionMethod">
              <Select
                v-model:value="reportForm.inspectionMethod"
                :options="dictStore.getDictList('FCT_INSPECTION_METHOD')"
                placeholder="请选择检查方式"
                class="w-full"
              />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="检查日期" name="inspectionDate">
              <DatePicker class="w-full" v-model:value="reportForm.inspectionDate" value-format="x" />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="项目类型" name="projectType">
              {{ dictStore.formatter(reportForm.projectType, 'FCT_PROJECT_TYPE') }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="增信措施" name="creditEnhancementDesc">
              {{ reportForm.creditEnhancementDesc || '-' }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="投放日期" name="investmentDate">
              {{ reportForm.investmentDate || '-' }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="投放金额（元）" name="investmentAmount">
              {{ formatMoney(reportForm.investmentAmount) || '-' }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="业务余额（元）" name="businessAmount">
              {{ formatMoney(reportForm.businessAmount) || '-' }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="内含报酬率（XIRR）" name="xirrRate">
              {{ reportForm.xirrRate || '-' }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="保理类型" name="factoringType">
              {{ dictStore.formatter(reportForm.factoringType, 'FCT_FACTORING_TYPE') || '-' }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="合同开始日期">
              {{ formatDate(reportForm.contractStartDate) || '-' }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="合同结束日期">
              {{ formatDate(reportForm.contractEndDate) || '-' }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="债权人" name="creditorList">
              {{ reportForm.creditorListString || '-' }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="债务人" name="debtorList">
              {{ reportForm.debtorListString || '-' }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="担保人" name="guarantorList">
              {{ reportForm.guarantorListString || '-' }}
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="应收账款概况" name="receivableDesc">
              <Input v-model:value="reportForm.receivableDesc" placeholder="请输入应收账款概况" />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="风险管理要求落实情况" name="riskManagementDesc">
              <Input v-model:value="reportForm.riskManagementDesc" placeholder="请输入风险管理要求落实情况" />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="资金流向" name="fundFlowDesc">
              <Input v-model:value="reportForm.fundFlowDesc" placeholder="请输入资金流向" />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="底层项目情况" name="underlyingProjectDesc">
              <Input v-model:value="reportForm.underlyingProjectDesc" placeholder="请输入底层项目情况" />
            </FormItem>
          </Col>
        </Row>
        <BasicCaption content="二、本季度回款履约情况" />
        <PerformanceTable />
        <BasicCaption content="三、债权人投后管理情况" />
        <a-tabs v-model:active-key="creditorActiveKey" v-if="reportForm.creditorList.length > 0">
          <a-tab-pane :key="v.companyCode" :tab="v.companyName" v-for="v in reportForm.creditorList">
            <div>
              <ScoreTable
                :ref="(el) => (creditorScoreTableRefs[v.companyCode] = el)"
                v-model="creditorScoreTableDataMap[v.companyCode]"
              />
              <div class="mt-5 flex justify-end">
                <span class="mr-3 self-center text-red-600">
                  提示：上传的财务报表文件名命名规则：XXXX年MM月-企业名称-财务报表.xlsx，
                  例如：2024年03月-三江贸易-财务报表.xlsx
                </span>
                <ImportData
                  title="上传财务报表"
                  style="margin-right: 10px"
                  multiple
                  :max-count="10"
                  :upload-api="importApi"
                  :download-template-api="downloadTemplateApi"
                  @import-success="(res) => handleImportSuccess(v.companyCode, res, 'creditor')"
                />
              </div>
              <FinancialReportTable style="padding: 5px" :table-data="creditorFinanceTableDataMap[v.companyCode]" />
            </div>
            <Col :span="24" style="margin-top: 10px">
              <FormItem label="财务分析" v-bind="fullProp">
                <Textarea
                  v-model:value="creditorFinancialAnalysisDesc[v.companyCode]"
                  :rows="4"
                  placeholder="请输入财务分析"
                />
              </FormItem>
            </Col>
          </a-tab-pane>
        </a-tabs>
        <Empty style="margin: 10px" v-else />
        <BasicCaption content="四、债务人投后管理情况" />
        <a-tabs v-model:active-key="debtorActiveKey" v-if="reportForm.debtorList?.length > 0">
          <a-tab-pane :key="v.companyCode" :tab="v.companyName" v-for="v in reportForm.debtorList">
            <div>
              <ScoreTable
                :ref="(el) => (debtorScoreTableRefs[v.companyCode] = el)"
                v-model="debtorScoreTableDataMap[v.companyCode]"
              />
            </div>
          </a-tab-pane>
        </a-tabs>
        <Empty style="margin: 10px" v-else />
        <BasicCaption content="五、担保人投后管理情况" />
        <a-tabs v-model:active-key="guarantorActiveKey" v-if="reportForm.guarantorList.length > 0">
          <a-tab-pane :key="v.companyCode" :tab="v.companyName" v-for="v in reportForm.guarantorList">
            <div>
              <ScoreTable
                :ref="(el) => (guarantorScoreTableRefs[v.companyCode] = el)"
                v-model="guarantorScoreTableDataMap[v.companyCode]"
              />
              <div class="mt-5 flex justify-end">
                <span class="mr-3 self-center text-red-600">
                  提示：上传的财务报表文件名命名规则：XXXX年MM月-企业名称-财务报表.xlsx，
                  例如：2024年03月-三江贸易-财务报表.xlsx
                </span>
                <ImportData
                  title="上传财务报表"
                  style="margin-right: 10px"
                  multiple
                  :max-count="10"
                  :upload-api="importApi"
                  :download-template-api="downloadTemplateApi"
                  @import-success="(res) => handleImportSuccess(v.companyCode, res, 'guarantor')"
                />
              </div>
              <FinancialReportTable
                style="margin-top: 10px"
                :table-data="guarantorFinanceTableDataMap[v.companyCode]"
              />
            </div>
            <Col :span="24" style="margin-top: 10px">
              <FormItem label="财务分析" v-bind="fullProp">
                <Textarea
                  v-model:value="guarantorFinancialAnalysisDesc[v.companyCode]"
                  :rows="4"
                  placeholder="请输入财务分析"
                />
              </FormItem>
            </Col>
          </a-tab-pane>
        </a-tabs>
        <Empty style="margin: 10px" v-else />
        <BasicCaption content="六、抵质押物检查情况" />
        <Row class="mt-5">
          <Col :span="24">
            <FormItem label="存续情况" name="continuationDesc" v-bind="fullProp">
              <Textarea v-model:value="reportForm.continuationDesc" :rows="4" placeholder="请输入存续情况" />
            </FormItem>
          </Col>
        </Row>
        <BasicCaption content="七、检查结果" />
        <Row class="mt-5">
          <Col :span="24">
            <FormItem label="其他情况" name="otherDesc" v-bind="fullProp">
              <Textarea v-model:value="reportForm.otherDesc" :rows="4" placeholder="请输入其他情况" />
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem label="检查结论" name="inspectionConclusionDesc" v-bind="fullProp">
              <Textarea v-model:value="reportForm.inspectionConclusionDesc" :rows="4" placeholder="请输入检查结论" />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="资产分类" name="assetClassification">
              <Select
                v-model:value="reportForm.assetClassification"
                :options="dictStore.getDictList('FCT_ASSET_CLASSIFY_TYPE')"
                placeholder="请选择资产分类"
                class="w-full"
              />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="运营人员" name="operationBys">
              <Select
                v-model:value="reportForm.operationBys"
                mode="multiple"
                allow-clear
                label-in-value
                show-search
                :options="userOptions"
                :field-names="{ label: 'realName', value: 'id' }"
                :filter-option="filterOption"
              />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="复核人员" name="reviewerBys">
              <Select
                v-model:value="reportForm.reviewerBys"
                allow-clear
                label-in-value
                show-search
                :options="userOptions"
                :field-names="{ label: 'realName', value: 'id' }"
                :filter-option="filterOption"
              />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="报告日期" name="reportDate">
              <DatePicker class="w-full" v-model:value="reportForm.reportDate" value-format="x" />
            </FormItem>
          </Col>
        </Row>
        <BaseAttachmentList
          v-model="reportForm.attachmentList"
          :business-id="reportForm.id"
          business-type="FCT_OPE_OPERATION_INSPECTION"
          edit-mode
        />
      </Form>
    </div>
  </BasicPopup>
</template>

<style scoped></style>
