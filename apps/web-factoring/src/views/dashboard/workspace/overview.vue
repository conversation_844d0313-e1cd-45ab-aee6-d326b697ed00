<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import * as echarts from 'echarts';

import ProjectIndustry from './components/project-industry.vue';
import ProjectType from './components/project-type.vue';
import { chinaMapObj } from './map.js';

const classifyChartRef = ref<EchartsUIType>();
const metricsChartRef = ref<EchartsUIType>();
const earlyChartRef = ref<EchartsUIType>();
const projectChartRef = ref<EchartsUIType>();
const { renderEcharts: classifyEcharts } = useEcharts(classifyChartRef);
const { renderEcharts: metricsEcharts } = useEcharts(metricsChartRef);
const { renderEcharts: earlyEcharts } = useEcharts(earlyChartRef);
const { renderEcharts: projectEcharts } = useEcharts(projectChartRef);

onMounted(async () => {
  echarts.registerMap('jiangxi', chinaMapObj);
  const data = [
    { name: '赣州市', value: 199 },
    { name: '吉安市', value: 39 },
    { name: '上饶市', value: 152 },
    { name: '九江市', value: 299 },
    { name: '抚州市', value: 89 },
    { name: '宜春市', value: 52 },
    { name: '南昌市', value: 9 },
    { name: '景德镇市', value: 352 },
    { name: '萍乡市', value: 99 },
    { name: '鹰潭市', value: 39 },
    { name: '新余市', value: 480 },
  ];
  await metricsEcharts({
    tooltip: {
      formatter(params) {
        // 若 params.data 是数组，遍历生成多数据行
        if (Array.isArray(params.data)) {
          let content = `<div style="font-weight: bold; margin-bottom: 6px;">${params.name}</div>`;
          params.data.forEach((item) => {
            content += `
          <div style="display: flex; justify-content: space-between; margin: 4px 0;">
            <span>${item.name || '指标'}：</span>
            <span>${item.value === undefined ? '无数据' : item.value}</span>
          </div>
        `;
          });
          return content;
        }
        // 非数组时的兜底展示
        return params.name;
      },
      borderWidth: 1,
      borderRadius: 6,
      padding: 10,
    },
    visualMap: {
      show: false,
      seriesIndex: [3],
      inRange: {
        color: ['#E6F7FF', '#81D4FA'], // 浅蓝系渐变，适配白色背景
      },
    },
    geo: [
      {
        map: 'jiangxi',
        roam: false,
        zoom: 1.1,
        scaleLimit: { min: 0, max: 3 },
        itemStyle: {
          normal: {
            areaColor: '#E6F3FF', // 地图底层极浅蓝，不抢主体
            shadowColor: '#CCE5FF', // 浅蓝阴影，弱化沉重感
            shadowBlur: 10,
            shadowOffsetX: -2,
            shadowOffsetY: 5,
          },
        },
        tooltip: { show: false },
      },
    ],
    series: [
      // 散点1：天蓝色系
      {
        type: 'effectScatter',
        coordinateSystem: 'geo',
        z: 5,
        data: [],
        symbolSize: 14,
        label: {
          normal: {
            show: true,
            formatter(params) {
              return `{fline|地点：${params.data.city}}\n{tline|${params.data.info || '发生xx集件'}}`;
            },
            position: 'top',
            backgroundColor: 'rgba(135, 206, 235, 0.8)',
            padding: [0, 0],
            borderRadius: 3,
            lineHeight: 32,
            color: '#ffffff',
            rich: {
              fline: { padding: [0, 10, 10, 10], color: '#ffffff' },
              tline: { padding: [10, 10, 0, 10], color: '#ffffff' },
            },
          },
          emphasis: { show: true }, // 散点悬浮时标签正常显示
        },
        itemStyle: { color: '#87CEEB' },
      },
      // 散点2：亮浅蓝色系
      {
        type: 'effectScatter',
        coordinateSystem: 'geo',
        z: 5,
        data: [],
        symbolSize: 14,
        label: {
          normal: {
            show: true,
            formatter(params) {
              return `{fline|地点：${params.data.city}}\n{tline|${params.data.info || '发生xx集件'}}`;
            },
            position: 'top',
            backgroundColor: 'rgba(79, 195, 247, 0.8)',
            padding: [0, 0],
            borderRadius: 3,
            lineHeight: 32,
            color: '#ffffff',
            rich: {
              fline: { padding: [0, 10, 10, 10], color: '#ffffff' },
              tline: { padding: [10, 10, 0, 10], color: '#ffffff' },
            },
          },
          emphasis: { show: true },
        },
        itemStyle: { color: '#4FC3F7' },
      },
      // 散点3：稍深浅蓝色系
      {
        type: 'effectScatter',
        coordinateSystem: 'geo',
        z: 5,
        data: [],
        symbolSize: 14,
        label: {
          normal: {
            show: true,
            formatter(params) {
              return `{fline|地点：${params.data.city}}\n{tline|${params.data.info || '发生xx集件'}}`;
            },
            position: 'top',
            backgroundColor: 'rgba(33, 150, 243, 0.8)',
            padding: [0, 0],
            borderRadius: 3,
            lineHeight: 32,
            color: '#ffffff',
            rich: {
              fline: { padding: [0, 10, 10, 10], color: '#ffffff' },
              tline: { padding: [10, 10, 0, 10], color: '#ffffff' },
            },
          },
          emphasis: { show: true },
        },
        itemStyle: { color: '#2196F3' },
      },
      // 核心地图层（修复文字消失问题）
      {
        type: 'map',
        mapType: 'jiangxi',
        geoIndex: -1,
        zoom: 1.1,
        label: {
          show: true, // 正常状态下显示地名
          color: '#4A90E2', // 正常地名色：淡蓝色，适配白色背景
          emphasis: {
            show: true, // 关键修改：鼠标悬浮时显示文字（原配置为false导致消失）
            color: '#ffffff', // 高亮时文字为白色，与高亮背景对比清晰
            fontSize: 14, // 微调高亮文字大小，提升可读性
            fontWeight: 500, // 文字稍加粗，避免过细看不清
          },
        },
        itemStyle: {
          normal: {
            borderColor: '#99CCFF', // 淡蓝边框，弱化割裂感
            borderWidth: 1,
            areaColor: '#F0F7FF', // 地图浅蓝底色，与geo层呼应
          },
          emphasis: {
            areaColor: '#64B5F6', // 微调高亮色：稍深的浅蓝，比之前更显质感
            borderWidth: 1, // 增加高亮边框，强化区域轮廓
            borderColor: '#2196F3', // 高亮边框色：稍深的蓝，提升层次感
          },
        },
        data: Object.keys(data).map((name) => {
          return { name, value: Math.random() * 100 };
        }),
      },
    ],
  });
  earlyEcharts({
    grid: {
      top: '5%',
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: [
      {
        splitLine: {
          show: false,
        },
        type: 'value',
        show: false,
      },
    ],
    yAxis: [
      {
        splitLine: {
          show: false,
        },
        axisLine: {
          // y轴
          show: false,
        },
        type: 'category',
        axisTick: {
          show: false,
        },
        data: ['高风险', '已处理', '风险预警'],
        axisLabel: {},
      },
    ],
    series: [
      {
        name: '标准化',
        type: 'bar',
        barWidth: 10, // 柱子宽度
        label: {
          show: true,
          position: 'right', // 位置
          color: '#1CD8A8',
          fontSize: 14,
          fontWeight: 'bold', // 加粗
          distance: 2, // 距离
        }, // 柱子上方的数值
        itemStyle: {
          barBorderRadius: [0, 20, 20, 0], // 圆角（左上、右上、右下、左下）
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            1,
            0,
            ['#2FAEF2', '#1CD8A8'].map((color, offset) => ({
              color,
              offset,
            })),
          ), // 渐变
        },
        data: [320, 302, 341, 374, 390, 650, 420],
      },
    ],
  });
  classifyEcharts({
    radar: {
      indicator: [{ name: '网页' }, { name: '移动端' }, { name: 'Ipad' }, { name: '客户端' }, { name: '第三方' }],
      radius: '70%',
      splitNumber: 8,
      // 调整网格线颜色适应白色背景
      splitLine: {
        lineStyle: {
          color: 'rgba(220, 231, 243, 0.8)', // 浅蓝灰色网格线
        },
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(152, 193, 217, 0.6)', // 浅蓝色轴线
        },
      },
      name: {
        textStyle: {
          color: '#4A6FA5', // 指标名称颜色（深蓝灰色）
        },
      },
    },
    series: [
      {
        areaStyle: {
          opacity: 0.3, // 透明度设置（0.3-0.5之间效果最佳）
          shadowBlur: 5,
          shadowColor: 'rgba(100, 181, 246, 0.4)', // 浅蓝色阴影
          shadowOffsetX: 2,
          shadowOffsetY: 2,
        },
        data: [
          {
            itemStyle: {
              color: '#64B5F6', // 基础浅蓝色
              borderColor: '#64B5F6', // 边框同色
              borderWidth: 2,
            },
            name: '访问',
            value: [90, 50, 86, 40, 50],
          },
        ],
        lineStyle: {
          color: '#64B5F6', // 线条颜色与填充色一致
          width: 2,
        },
        symbolSize: 6, // 适当显示数据点（原0隐藏）
        symbol: 'circle',
        type: 'radar',
      },
    ],
    tooltip: {},
  });

  projectEcharts({
    grid: {
      top: '100',
      bottom: '3%',
      containLabel: true,
    },
    legend: {
      top: '10',
      data: ['本月新增项目数量', '本月新增项目投放金额', '本月结清项目数量', '结清项目累计收回金额'],
    },
    xAxis: [
      {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        axisPointer: {
          type: 'shadow',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量',
      },
      {
        type: 'value',
        name: '金额',
      },
    ],
    series: [
      {
        name: '本月新增项目数量',
        type: 'bar',
        itemStyle: { color: '#66B3FF' }, // 浅蓝色（匹配需求色）
        tooltip: {
          formatter: '{seriesName}：{value} 个',
        },
        data: [2, 4.9, 7, 23.2, 25.6, 76.7, 135.6],
      },
      {
        name: '本月新增项目投放金额',
        type: 'line',
        yAxisIndex: 1,
        itemStyle: { color: '#FF4D4F' }, // 红色（匹配需求色）
        tooltip: {
          formatter: '{seriesName}：{value} 万元',
        },
        data: [2.6, 5.9, 9, 26.4, 28.7, 70.7, 175.6],
      },
      {
        name: '本月结清项目数量',
        type: 'bar',
        itemStyle: { color: '#36CFC9' },
        tooltip: {
          formatter: '{seriesName}：{value} 个',
        },
        data: [2, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3],
      },
      {
        name: '结清项目累计收回金额',
        type: 'line',
        yAxisIndex: 1,
        itemStyle: { color: '#FAAD14' },
        tooltip: {
          formatter: '{seriesName}：{value} 万元',
        },
        data: [2, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3],
      },
    ],
  });
});
</script>

<template>
  <div class="screen-wrap">
    <a-row>
      <a-col :span="6" class="col-box">
        <div class="box-bottom box-bg box-type">
          <div class="box-title">进行中项目按类型分布</div>
          <div class="box-content">
            <ProjectType />
          </div>
        </div>
        <div class="box-bg box-industry">
          <div class="box-title">进行中项目按行业分布</div>
          <div class="box-content">
            <ProjectIndustry />
          </div>
        </div>
      </a-col>
      <a-col :span="12" class="col-center">
        <div class="box-bottom box-bg box-metrics">
          <div class="box-title-core">实时核心指标</div>
          <div class="box-card-metrics">
            <div class="card-group">
              <div class="card">
                <div class="card-number">55</div>
                <div class="card-desc">累计支持我省实体经济项目数量</div>
              </div>
              <div class="card">
                <div class="card-number">55</div>
                <div class="card-desc">累计支持我省实体经济规模（万元）</div>
              </div>
              <div class="card">
                <div class="card-number">55</div>
                <div class="card-desc">支持我省重点产业链规模（万元）</div>
              </div>
            </div>
          </div>
          <div class="box-card-metrics-extra">
            <div class="card-group">
              <div class="card">
                <div class="card-number-desc">55</div>
                <div class="card-desc">项目数量</div>
              </div>
              <div class="card">
                <div class="card-number-desc">55</div>
                <div class="card-desc">项目授信总额(万元)</div>
              </div>
              <div class="card">
                <div class="card-number-desc">55</div>
                <div class="card-desc">项目累计投放金额(万元)</div>
              </div>
              <div class="card">
                <div class="card-number-desc">55</div>
                <div class="card-desc">项目业务余额(万元)</div>
              </div>
              <div class="card">
                <div class="card-number-desc">55</div>
                <div class="card-desc">业务余额季度环比</div>
              </div>
            </div>
          </div>
          <EchartsUI ref="metricsChartRef" class="box-content-metrics" />
        </div>
        <div class="box-bg box-client">
          <div class="box-title">服务客户</div>
          <div class="box-card-client">
            <div class="card-group">
              <div class="card">
                <div class="card-number">55</div>
                <div class="card-desc">累计服务客户数量</div>
                <div class="card-extra">债权人: 25, 债务人20, 担保人: 10</div>
              </div>
              <div class="card">
                <div class="card-number">55</div>
                <div class="card-desc">合作中客户数量</div>
                <div class="card-extra">债权人: 25, 债务人20, 担保人: 10</div>
              </div>
              <div class="card">
                <div class="card-number">55</div>
                <div class="card-desc">本年新增客户数量</div>
                <div class="card-extra">债权人: 25, 债务人20, 担保人: 10</div>
              </div>
            </div>
          </div>
        </div>
      </a-col>
      <a-col :span="6" class="col-box">
        <div class="box-bottom box-bg box-early">
          <div class="box-title">风险预警</div>
          <EchartsUI ref="earlyChartRef" class="box-content" />
        </div>
        <div class="box-bottom box-bg box-classify">
          <div class="box-title">资产风险分类</div>
          <EchartsUI ref="classifyChartRef" class="box-content" />
        </div>
        <div class="box-bg box-project">
          <div class="box-title">项目数据</div>
          <EchartsUI ref="projectChartRef" class="box-content-project" />
          <a-row class="box-project-overview">
            <a-col :span="12" class="project-overview-data"> 新增项目数量: 555 </a-col>
            <a-col :span="12" class="project-overview-data"> 结清项目数量: 555 </a-col>
            <a-col :span="12" class="project-overview-data"> 新增投放金额: 555 </a-col>
            <a-col :span="12" class="project-overview-data"> 结清项目金额: 555 </a-col>
          </a-row>
        </div>
      </a-col>
    </a-row>
  </div>
</template>
<style lang="scss">
.screen-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 10px;
  overflow: hidden;

  .ant-row {
    height: 100%;
  }

  .box-padding-top {
    padding-top: 10px;
  }

  .box-padding-bottom {
    padding-bottom: 10px;
  }

  .box-bg {
    padding: 0 10px;
    background-color: #fff;
    border-radius: 10px;
  }

  .box-bottom {
    margin-bottom: 10px;
  }

  .box-type {
    height: 40%;
  }

  .box-industry {
    height: calc(100% - 40% - 10px);
  }

  .col-center {
    padding: 0 10px;
  }

  .box-metrics {
    height: 82%;
  }

  .box-client {
    height: calc(100% - 82% - 10px);
  }

  .box-early {
    height: 15%;
  }

  .box-classify {
    height: 30%;
  }

  .box-project {
    height: calc(100% - 45% - 20px);
  }

  .box-title {
    height: 30px;
    font-size: 14px;
    line-height: 30px;
  }

  .box-content {
    height: calc(100% - 30px) !important;
    overflow: hidden;
  }

  .box-content-metrics {
    height: calc(100% - 27%) !important;
    overflow: hidden;
  }

  .box-title-core {
    height: 6%;
    font-size: 2.6vh;
    font-weight: bold;
    line-height: 30px;
    text-align: center;
  }

  .box-card-metrics {
    height: 12%;

    .card-group {
      padding-bottom: 10px;
    }
  }

  .box-card-metrics-extra {
    height: 9%;

    .card-group {
      padding-bottom: 0;
    }
  }

  .box-card-client {
    box-sizing: border-box;
    height: calc(100% - 30px) !important;
    padding: 10px 0;
    overflow: hidden;
  }

  .box-content-project {
    height: calc(100% - 80px) !important;
    overflow: hidden;
  }

  .box-project-overview {
    height: 40px !important;

    .project-overview-data {
      height: 20px;
      font-size: 12px;
      line-height: 20px;
    }
  }

  /* 卡片容器：横向排列 + 间距 */
  .card-group {
    display: flex;
    gap: 10px;
    height: 100%;
    overflow: hidden;
  }

  /* 单个卡片：浅蓝背景 + 圆角 + 内边距 */
  .card {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    background: #f0f7ff; /* 浅蓝卡片背景 */
    border-radius: 8px;
    box-shadow: 0 2px 8px rgb(0 0 128 / 8%); /* 轻微阴影增强层次 */
  }

  /* 卡片头部：图标 + 标题 */
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  /* 卡片标题文字 */
  .card-title {
    font-size: 14px;
    color: #333;
  }

  /* 核心数值（大字号加粗） */
  .card-number {
    font-size: 2.8vh;
    font-weight: bold;
    color: #333;
  }

  .card-number-desc {
    font-size: 2.4vh;
    font-weight: bold;
    color: #333;
  }

  /* 描述文字（中号字） */
  .card-desc {
    font-size: 1.4vh;
    color: #666;
  }

  /* 底部补充信息（小号浅灰字） */
  .card-extra {
    font-size: 1.4vh;
    color: #999;
  }
}
</style>
