<script setup lang="ts">
import type { PaymentRecordInfo } from '#/api';

import { reactive, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { formatMoney } from '@vben/utils';

import { message, Select } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep, omit } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import {
  addRepaymentRecordApi,
  editRepaymentRecordApi,
  getRepaymentPlanInfoApi,
  getRepaymentPlanListApi,
  getRepaymentRecordInfoApi,
} from '#/api';
import InterestFees from '#/views/fund/components/interest-fees.vue';
import RepaymentPlan from '#/views/fund/components/repayment-plan.vue';

const emit = defineEmits(['ok', 'register']);
const dictStore = useDictStore();
const init = async (data: PaymentRecordInfo) => {
  recordForm.value = {};
  if (data.id) {
    const info = data.id ? await getRepaymentRecordInfoApi(data.id as number) : data;
    info.repaymentDate = info.repaymentDate ? dayjs(info.repaymentDate).valueOf().toString() : '';
    recordForm.value = { ...recordForm.value, ...info };
    RepaymentPlanRef.value.init(recordForm.value);
  }
  if (data.formType === 'repaymentPlan') {
    recordForm.value.repaymentPlanId = Number(data.repaymentPlanId);
    data.value = data.repaymentPlanId;
    await selectRepaymentPlan(data.repaymentPlanId, data);
  }
};

const selectRepaymentPlan = async (_value: number, data: PaymentRecordInfo) => {
  const info = await getRepaymentPlanInfoApi(data.value);
  const repaymentPlan = omit(info, 'id');
  recordForm.value = { ...recordForm.value, ...repaymentPlan };
  RepaymentPlanRef.value.init(recordForm.value);
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const recordForm = ref<PaymentRecordInfo>({});

const loading = reactive({
  submit: false,
});
const RepaymentPlanRef = ref();
const save = async (type: string) => {
  await FormRef.value.validate();
  const repaymentResult = await RepaymentPlanRef.value.save();
  if (!repaymentResult) {
    return;
  }
  let api = addRepaymentRecordApi;
  if (recordForm.value.id) {
    api = editRepaymentRecordApi;
  }
  const formData = cloneDeep(recordForm.value);
  formData.repaymentDate = Number(formData.repaymentDate);
  loading.submit = true;
  if (type === 'submit') formData.isSubmit = true;
  try {
    const res = await api(formData as PaymentRecordInfo);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
const rules = {
  repaymentPlanId: [{ required: true, message: '请选择关联还款计划编号', trigger: 'change' }],
  repaymentDate: [{ required: true, message: '请选择还款日期', trigger: 'change' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 7 }, wrapperCol: { span: 17 } };
const colSpan = COL_SPAN_PROP;
</script>

<template>
  <BasicPopup v-bind="$attrs" title="还款记录" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="recordForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="还款记录编号" name="projectName">
              {{ recordForm.repaymentConfirmCode }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="关联还款计划编号" name="repaymentPlanId">
              <ApiComponent
                v-model="recordForm.repaymentPlanId as unknown as string"
                :component="Select"
                :api="getRepaymentPlanListApi"
                label-field="repaymentPlanCode"
                value-field="id"
                model-prop-name="value"
                @change="selectRepaymentPlan"
                show-search
                :filter-option="(input: string, option: any) => option.label.includes(input)"
                placeholder="请选择关联还款计划编号"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目名称">
              {{ recordForm.projectName }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目类型">
              {{ dictStore.formatter(recordForm.projectType, 'FCT_PROJECT_TYPE') }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="关联用信申请">
              {{ recordForm.creditApplyName }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="关联付款申请编号">
              {{ recordForm.paymentApplyCode }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="关联付款记录">
              {{ recordForm.paymentConfirmCode }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="还款金额（元）">
              {{ formatMoney(recordForm.repaymentAmount) }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="还款日期" name="repaymentDate">
              <a-date-picker v-model:value="recordForm.repaymentDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
        <InterestFees :interest-form="recordForm" />
        <RepaymentPlan ref="RepaymentPlanRef" v-model="recordForm" calculation-type="RepaymentRecord" />
        <BaseAttachmentList
          v-model="recordForm.attachmentList"
          :business-id="recordForm.id"
          business-type="FCT_REPAYMENT_CONFIRM"
          edit-mode
        />
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
